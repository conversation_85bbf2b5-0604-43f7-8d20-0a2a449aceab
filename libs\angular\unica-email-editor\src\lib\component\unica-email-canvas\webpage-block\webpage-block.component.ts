import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FocusableEmailElement } from '../../../config/email-element-drop-list';
import { GenerateUniqueId } from '@hcl/unica-common';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailDefaultService } from '../../../service/email-default.service';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { EmailElementType } from '../../../config/element-toolbar';
import {
  ElementAttribute,
  ElementBackground,
  ElementBorder,
  ElementFont,
  ElementLineHeight,
  ElementStyle,
  ElementWidthHeight,
  HideOnType,
  StyleAttribute,
} from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { ElementBackgroundDirective } from '../../../directive/element-background.directive';
import { ElementBorderDirective } from '../../../directive/element-border.directive';
import { ElementFontDirective } from '../../../directive/element-font.directive';
import { ElementFullWidthDirective } from '../../../directive/element-full-width.directive';
import { ElementColorDirective } from '../../../directive/element-color.directive';
import { ElementLineHeightDirective } from '../../../directive/element-line-height.directive';
import { EmailBlock, UnicaWebpageBlock } from '../../../config/email';
import { BlockActionsService } from '../../../service/block-actions.service';
import { ElementHeightDirective } from '../../../directive/element-height.directive';

@Component({
  selector: 'webpage-block',
  standalone: true,
  imports: [
    CommonModule,
    LetDirective,
    ElementHeightDirective,
    ElementBackgroundDirective,
    ElementBorderDirective,
    ElementFontDirective,
    ElementFullWidthDirective,
    ElementColorDirective,
    ElementLineHeightDirective,
    FocusOverlayComponent,
  ],
  templateUrl: './webpage-block.component.html',
  styleUrl: './webpage-block.component.scss',
})
export class WebpageBlockComponent implements FocusableEmailElement {
  /**
   * The unique id
   */
  private _id: string = GenerateUniqueId(13);
  private _dropListId = '';
  private _blockIndex = 0;

  get id(): string {
    return this._dropListId
      ? `${this._dropListId}_${this._blockIndex}`
      : this._id;
  }

  @Input() set dropListId(value: string) {
    this._dropListId = value;
  }

  @Input() set blockIndex(value: number) {
    this._blockIndex = value;
  }
  /**
   * The input for the block
   */
  private _block = new UnicaWebpageBlock();
  private blockSubject = new BehaviorSubject<UnicaWebpageBlock>(this._block);
  protected block$ = this.blockSubject
    .asObservable()
    .pipe(tap((b) => (this._block = b)));
  @Input({
    transform: (value: EmailBlock): UnicaWebpageBlock =>
      <UnicaWebpageBlock>value,
  })
  set block(b: UnicaWebpageBlock) {
    this.blockSubject.next(b);
    this.backgroundSubject.next(b.options.background);
    this.borderSubject.next(b.options.border);
    this.fontSubject.next(b.options.font);
    this.colorSubject.next(b.options.color);
    this.lineHeightSubject.next(b.options.lineHeight);
    this.innerTextSubject.next(b.innerText);
    this.fullWidthSubject.next(b.options.fullWidth);
    this.hideOnSubject.next(b.options.hideOn);
  }
  /**
   * The style
   * @protected
   */
  protected style$ = this.block$.pipe(
    map((b) => {
      if (b) {
        return b.options;
      }
      return undefined;
    }),
  );

  /**
   * For canvas, Whe it is empty we have below actions
   */
  protected webpageToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings',
    },
    {
      id: 'duplicate',
      icon: 'content_copy',
      label: 'Duplicate',
    },
    {
      id: 'delete',
      icon: 'delete',
      label: 'Delete',
    },
  ];
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged(),
  );
  /**
   *
   * @param defaultService
   */
  constructor(
    private defaultService: EmailDefaultService,
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private blockActionsService: BlockActionsService,
  ) {}
  /**
   * Set the focus on canvas, this is called when the user clicks on
   * any empty location on the canvas
   */
  public focus(event?: Event): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   * Called when a element in the toolbar is selected by the user
   */
  protected performAction({ item, event }: { item: ElementToolbarItemConfig, event: MouseEvent | KeyboardEvent}) {
    switch (item.id) {
      default:
      case 'settings':
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.WEBPAGE_SETTINGS);
        break;
      case 'duplicate': {
        this.blockActionsService.duplicateBlock(this);
        break;
      }
      case 'delete': {
        this.blockActionsService.deleteBlock(this);
        break;
      }
    }
  }

  /**
   * Get the style of this button
   */
  getStyle(): ElementStyle {
    return this._block.options;
  }

  updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'border' && value && value.border) {
      this.borderSubject.next(<ElementBorder>value.border);
    } else if (attribute === 'font' && value && value.font) {
      this.fontSubject.next(<ElementFont>value.font);
    } else if (attribute === 'color' && value && value.color) {
      this.colorSubject.next(<string>value.color);
    } else if (attribute === 'lineHeight' && value && value.lineHeight) {
      this.lineHeightSubject.next(<ElementLineHeight>value.lineHeight);
    } else if (attribute === 'background' && value && value.background) {
      this.backgroundSubject.next(<ElementBackground>value.background);
    } else if (
      attribute === 'fullWidth' &&
      value &&
      value.fullWidth !== undefined
    ) {
      // we are checking for undefined because fullWidth can be false
      this.fullWidthSubject.next(<boolean>value.fullWidth);
    } else if (attribute === 'hideOn' && value && value.hideOn !== undefined) {
      // we are checking for undefined because hideOn can be empty string also
      this.hideOnSubject.next(<HideOnType>value.hideOn);
    }
  }
  /**
   * method to return any non style attributes like innerText in button block
   * @returns The element attribute
   */
  getElementAttribute(): ElementAttribute | undefined {
    return {
      innerText: this._block.innerText,
    };
  }

  /**
   * method to update any non style attributes like innerText in button block
   */
  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {
    if (attribute === 'innerText' && value && value.innerText !== undefined) {
      // we are checking for undefined because innerText can be empty string
      // and we need to set it to empty string
      this.innerTextSubject.next(value.innerText);
    }
  }

  /**
   * The innerText of this block
   */
  private innerTextSubject = new BehaviorSubject<string | undefined>(undefined);
  protected innerTextSubject$ = this.innerTextSubject.asObservable().pipe(
    tap((w) => {
      if (this._block) {
        this._block.innerText = w ?? '';
      }
    }),
  );

  /**
   * The border of this block
   */
  private borderSubject = new BehaviorSubject<ElementBorder | undefined>(
    undefined,
  );
  protected borderSubject$ = this.borderSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.border = w;
      }
    }),
  );

  /*
   * The font of this block
   */
  private fontSubject = new BehaviorSubject<ElementFont | undefined>(undefined);
  protected fontSubject$ = this.fontSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.font = w;
      }
    }),
  );

  /*
   * The color of this block
   */
  private colorSubject = new BehaviorSubject<string | undefined>(undefined);
  protected colorSubject$ = this.colorSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.color = w;
      }
    }),
  );

  /**
   * The line height of this block
   */
  private lineHeightSubject = new BehaviorSubject<
    ElementLineHeight | undefined
  >(undefined);
  protected lineHeightSubject$ = this.lineHeightSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.lineHeight = w;
      }
    }),
  );

  /*
   * The hide On of this block
   */
  private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
    undefined,
  );
  protected hideOnSubject$ = this.hideOnSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.hideOn = w || undefined;
      }
    }),
  );

  /**
   * The background of this block
   */
  private backgroundSubject = new BehaviorSubject<
    ElementBackground | undefined
  >(undefined);
  protected backgroundSubject$ = this.backgroundSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.background = w;
      }
    }),
  );

  /*
   * The full Width of this block
   */
  private fullWidthSubject = new BehaviorSubject<boolean | undefined>(
    undefined,
  );
  protected fullWidthSubject$ = this.fullWidthSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.fullWidth = w;
      }
    }),
  );
}
