import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  Renderer2,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import {
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  ConnectedPosition,
} from '@angular/cdk/overlay';
import { LetDirective } from '@ngrx/component';
import { UnicaInputComponent } from '@hcl/angular/unica-input';
import { UnicaIconModule } from '@hcl/angular/unica-icon';
import {
  ElementBackground,
  ElementBorder,
  ElementPaddingMargin,
  ElementWidthHeight,
  HideOnType,
  Align,
  VerticalAlign,
} from '../../../config/email-common-elements';
import { ElementWidthDirective } from '../../../directive/element-width.directive';
import { ElementBackgroundDirective } from '../../../directive/element-background.directive';
import { ElementPaddingDirective } from '../../../directive/element-padding.directive';
import { UnicaButtonModule } from '@hcl/angular/unica-button';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { ElementBorderDirective } from '../../../directive/element-border.directive';
import { ElementAlignDirective } from '../../../directive/element-align.directive';
import { EmailDropListRegistryService } from '../../../service/email-drop-list-registry.service';
import { CdkDragHandle } from '@angular/cdk/drag-drop';

@Component({
  selector: 'focus-overlay',
  standalone: true,
  imports: [
    CommonModule,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    LetDirective,
    UnicaInputComponent,
    UnicaIconModule,
    ElementWidthDirective,
    ElementBackgroundDirective,
    ElementPaddingDirective,
    UnicaButtonModule,
    ElementBorderDirective,
    ElementAlignDirective,
    CdkDragHandle,
  ],
  templateUrl: './focus-overlay.component.html',
  styleUrl: './focus-overlay.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FocusOverlayComponent implements AfterViewInit {
  /**
   * The items that we will display on the element
   */
  @Input() items: ElementToolbarItemConfig[] = [];
  /**
   * The items that we will display on the element
   */
  @Input() structureToolbarItems: ElementToolbarItemConfig[] = [];

  @ViewChild('elementToolbar') elementToolbar!: CdkOverlayOrigin;
  @ViewChild('structureToolbar') structureToolbar!: CdkOverlayOrigin;

  // Add ViewChild references to the actual overlay templates
  @ViewChild('elementToolbarOverlay') elementToolbarOverlay!: CdkConnectedOverlay;
  @ViewChild('structureToolbarOverlay') structureToolbarOverlay!: CdkConnectedOverlay;

  /**
   * Based on this the dom node that is wrapped within this
   * component will be focused
   */
  private activeSubject = new BehaviorSubject(false);
  protected active$ = this.activeSubject
    .asObservable()
    .pipe(distinctUntilChanged());
  @Input() set isActive(b: boolean) {
    this.activeSubject.next(b);
  }
  /**
   * The default canvas width
   */
  private width: ElementWidthHeight | null = null;
  private widthSubject = new BehaviorSubject<ElementWidthHeight | null>(null);
  protected width$ = this.widthSubject
    .asObservable()
    .pipe(tap((w) => (this.width = w)));
  @Input() set nodeWidth(w: ElementWidthHeight | null) {
    this.widthSubject.next(w);
  }
  /**
   * The default canvas width
   */
  private background: ElementBackground | null = null;
  private backgroundSubject = new BehaviorSubject<ElementBackground | null>(
    null,
  );
  protected background$ = this.backgroundSubject
    .asObservable()
    .pipe(tap((bg) => (this.background = bg)));
  @Input() set nodeBackground(bg: ElementBackground | null) {
    this.backgroundSubject.next(bg);
  }
  /**
   * The default canvas width
   */
  private border: ElementBorder | null = null;
  private borderSubject = new BehaviorSubject<ElementBorder | null>(null);
  protected border$ = this.borderSubject
    .asObservable()
    .pipe(tap((bg) => (this.border = bg)));
  @Input() set nodeBorder(bg: ElementBorder | null) {
    this.borderSubject.next(bg);
  }
  /**
   * The default canvas padding
   */
  private padding: ElementPaddingMargin | null = null;
  private paddingSubject = new BehaviorSubject<ElementPaddingMargin | null>(
    null,
  );
  protected padding$ = this.paddingSubject
    .asObservable()
    .pipe(tap((p) => (this.padding = p)));
  @Input() set nodePadding(p: ElementPaddingMargin | null) {
    this.paddingSubject.next(p);
  }

  /**
   * The default canvas padding
   */
  private hideOn: HideOnType | undefined = undefined;
  private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
    undefined,
  );
  protected hideOn$ = this.hideOnSubject
    .asObservable()
    .pipe(tap((h) => (this.hideOn = h)));
  @Input() set nodeHideOn(h: HideOnType | undefined) {
    this.hideOnSubject.next(h);
  }
  /**
   * The alignment for the element
   */
  private align: Align | VerticalAlign | null = null;
  private alignSubject = new BehaviorSubject<Align | VerticalAlign | null>(
    null,
  );
  protected align$ = this.alignSubject.asObservable().pipe(
    tap((align) => {
      this.align = align;
    }),
  );
  @Input() set nodeAlign(align: Align | VerticalAlign | null) {
    this.alignSubject.next(align);
  }
  /**
   * Tells if we should show the drag handle
   */
  @Input() draggable = true;
  /**
   * Teh drag node is inside the focus block
   */
  @Input() invertedDraggable = false;
  /**
   * The css for active CSS
   */
  @Input() activeCss = 'element-active';
  /**
   * The hover css
   */
  @Input() hoverCss = '';
  /**
   * Called when a toolbar item is sselected
   */
  @Output() select = new EventEmitter<{
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }>();
  /**
   * The container on which the css will be applied
   */
  @ViewChild('container') container!: ElementRef;
  /**
   * Subject based on which the drag will be visible on hover
   * @private
   */
  private hoverActiveSubject = new BehaviorSubject(false);
  protected hoverActive$ = this.hoverActiveSubject
    .asObservable()
    .pipe(distinctUntilChanged());
  /**
   * Tells if the drag is in progress
   * @protected
   */
  protected dragInProgress$ = this.dropListRegistry.currentActiveDropList$.pipe(
    map((id) => (id ? true : false)),
  );

  /**
   * The position how the overlay needs to be displayed
   */
  protected dragHandlePosition: ConnectedPosition[] = [
    // this is when the toolbar is on the right
    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },
  ];
  /**
   * The position how the overlay needs to be displayed
   */
  protected toolbarPosition: ConnectedPosition[] = [
    // this is when the toolbar is on the left
    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },
  ];
  /**
   * The position how the overlay needs to be displayed
   */
  protected structurePosition: ConnectedPosition[] = [
    // this is when the toolbar is on the left
    { originX: 'center', originY: 'bottom', overlayX: 'end', overlayY: 'top' },
  ];
  /**
   * @param canvasService
   */
  constructor(
    protected dropListRegistry: EmailDropListRegistryService,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef
  ) {}
  ngAfterViewInit() {
    // Log the state of the overlays
    console.log('After view init:', {
      elementToolbarOverlay: this.elementToolbarOverlay,
      structureToolbarOverlay: this.structureToolbarOverlay
    });
  }
  /**
   * Called when user selects a toolbar item
   */
  protected toolbarItemSelected(
    item: ElementToolbarItemConfig,
    event: MouseEvent | KeyboardEvent,
  ) {
    this.select.next({ item, event });
  }
  /**
   * Mouse enter on the focusable element
   * @protected
   */
  protected mouseEnter(): void {
    if (this.hoverCss && this.hoverCss !== '') {
      this.renderer.addClass(this.container.nativeElement, this.hoverCss);
      this.hoverActiveSubject.next(true);
    }
  }
  /**
   * Mouse leave on the focusable element
   * @protected
   */
  protected mouseLeave(): void {
    if (this.hoverCss && this.hoverCss !== '') {
      this.renderer.removeClass(this.container.nativeElement, this.hoverCss);
      this.hoverActiveSubject.next(false);
    }
  }

  protected readonly DragEvent = DragEvent;
  /**
   * Public method to manually reposition overlays
   */
  repositon() {
    console.log('Reposition called:', {
      elementToolbarOverlay: this.elementToolbarOverlay,
      structureToolbarOverlay: this.structureToolbarOverlay
    });

    // Force a change detection cycle
    this.cdr.detectChanges();

    // Try to access the overlays after a short delay
    setTimeout(() => {
      console.log('After timeout:', {
        elementToolbarOverlay: this.elementToolbarOverlay,
        structureToolbarOverlay: this.structureToolbarOverlay
      });

      if (this.elementToolbarOverlay) {
        if (this.elementToolbarOverlay.overlayRef) {
          this.elementToolbarOverlay.overlayRef.updatePosition();
        }
      }

      if (this.structureToolbarOverlay) {
        if (this.structureToolbarOverlay.overlayRef) {
          this.structureToolbarOverlay.overlayRef.updatePosition();
        }
      }
    }, 100);
  }
}
