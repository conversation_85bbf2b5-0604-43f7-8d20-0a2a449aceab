<div class="editor-sub-menu-container">
  <div class="sub-menu-title">
    <unica-typography [variant]="'h5'">
      {{title$ | async}}
    </unica-typography>
  </div>
  <div class="tab-container">
    @switch (type$ | async) {
      @default {
        <element-settings></element-settings>
      }
      @case (EmailElementType.TEXT) {
        <font-listing></font-listing>
      }
      @case (EmailElementType.STRUCTURE) {
        <structure-listing></structure-listing>
      }
      @case (EmailElementType.STRUCTURE_SETTINGS) {
        <structure-settings></structure-settings>
      }
      @case (EmailElementType.SPACER) {
        <spacer-listing></spacer-listing>
      }
      @case (EmailElementType.IMAGE_SETTINGS) {
        <image-settings></image-settings>
      }
      @case (EmailElementType.BUTTON_SETTINGS) {
        <button-settings></button-settings>
      }
      @case (EmailElementType.SPACER_SETTINGS) {
        <spacer-settings></spacer-settings>
      }
      @case (EmailElementType.DIVIDER_SETTINGS) {
        <divider-settings></divider-settings>
      }
      @case (EmailElementType.WEBPAGE_SETTINGS) {
        <webpage-settings></webpage-settings>
      }
      @case (EmailElementType.PREFERENCES_UNSUBSCRIBE_SETTINGS) {
        <preferences-unsubscribe-settings></preferences-unsubscribe-settings>
      }
      @case (EmailElementType.COLUMN_SETTINGS) {
        <column-settings></column-settings>
      }
      @case (EmailElementType.CANVAS_SETTINGS) {
        <canvas-settings></canvas-settings>
      }
      @case (EmailElementType.TEXT_SETTINGS) {
        <text-settings></text-settings>
      }
      @case (EmailElementType.CONTENT_CONNECTOR_SETTINGS) {
        <content-connector-settings></content-connector-settings>
      }
    }
  </div>
</div>
