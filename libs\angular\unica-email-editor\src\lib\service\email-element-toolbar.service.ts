import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { EmailElementToolbarConfig, EmailElementType } from '../config/element-toolbar';
import { TranslateService } from '@ngx-translate/core';
import {
  Email<PERSON><PERSON>,
  UnicaButtonBlock,
  UnicaContentConnectorBlock,
  UnicaDividerBlock,
  UnicaImageBlock,
  UnicaSpacerBlock,
  UnicaStructure, UnicaWebpageBlock
} from '../config/email';

/**
 * this is a element toolbar service that can be used
 * to manipulate various functionally in the element toolbar from
 * outside the toolbar
 */
@Injectable()
export class EmailElementToolbarService {
  /**
   * This is the subject that will be fired when the elements that need to be displayed are finalized
   * based on permissions
   */
  private toolbarItemsSubject = new BehaviorSubject<EmailElementToolbarConfig[]>([]);
  public toolbarItems$ = this.toolbarItemsSubject.asObservable();
  /**
   * This is the subject that will be fired when the elements that need to be displayed are finalized
   * based on permissions
   */
  private activeSubMenuPanel = new BehaviorSubject<EmailElementType | undefined>(undefined);
  public activeSubMenu$ = this.activeSubMenuPanel.asObservable();
  /**
   * When we start dragging any element we have to close the
   * sub-menu, so this subject will be fired
   */
  public dragStartSubject = new BehaviorSubject<EmailBlock | UnicaStructure |  undefined>(undefined);
  public dragStart$ = this.dragStartSubject.asObservable();
  /**
   * The elements in the toolbar can be permission based & we may receive the details from teh server
   */
  constructor(private translate: TranslateService) {
    this.loadToolbarElements();
  }

  /**
   * As per requirement this function will get teh details from the server if required
   * or load it from the local storage
   */
  private loadToolbarElements() {
    // TODO: for now we will just fire the hard-coded value, this can be retrieved from the server
    this.toolbarItemsSubject.next([{
      id: 'structure',
      icon: 'unica_col_module',
      label: 'Column Module',
      order: 1,
      type: EmailElementType.STRUCTURE
    },
    {
      id: 'image_module',
      icon: 'unica_image_module',
      label: 'Image',
      order: 2,
      type: EmailElementType.IMAGE,
      draggable: true,
      // specify the default Image config, as this node is draggable
      config: new UnicaImageBlock()
    },
    {
      id: 'text',
      icon: 'unica_text_module',
      label: 'Text',
      order: 3,
      type: EmailElementType.TEXT
    },
    {
      id: 'button_module',
      icon: 'unica_button_module',
      label: 'Button',
      order: 4,
      type: EmailElementType.BUTTON,
      draggable: true,
      config: new UnicaButtonBlock(this.translate.instant('EMAIL_EDITOR.LABELS.CLICK_ON_ME')), // pass innerText in constructor if needed as done in old framework
      children: []
    },
    {
      id: 'spacer_module',
      icon: 'unica_spacer_module',
      label: 'Spacer',
      order: 5,
      draggable: true,
      type: EmailElementType.SPACER,
      config: new UnicaSpacerBlock(),
      children: []
    },
    {
      id: 'divider_module',
      icon: 'unica_divider_module',
      label: 'Divider',
      order: 6,
      type: EmailElementType.DIVIDER,
      config: new UnicaDividerBlock(),
      draggable: true,
      children: []
    },
    {
      id: 'social_module',
      icon: 'unica_social_module',
      label: 'Social',
      order: 7,
      type:  EmailElementType.SOCIAL,
      draggable: true,
      children: []
    },
    {
      id: 'webpage_module',
      icon: 'unica_webpage_module',
      label: 'Browser',
      order: 8,
      type: EmailElementType.WEBPAGE,
      draggable: true,
      config: new UnicaWebpageBlock(this.translate.instant('EMAIL_EDITOR.LABELS.OPEN_AS_WEBPAGE')),
      children: []
    },
    {
      id: 'snippet_module',
      icon: 'unica_snippet_module',
      label: 'Snippet',
      order: 9,
      type: EmailElementType.HTML,
      draggable: true,
      children: []
    },
    {
      id: 'content_connector_module',
      icon: 'unica_content_connector_module',
      label: 'External Content',
      order: 10,
      type: EmailElementType.CONTENTCONNECTOR,
      config: new UnicaContentConnectorBlock(),
      draggable: true,
      children: []
    }
  ]);
  }
  /**
   * Open the sub menu panel
   * @param id
   */
  public openPanel(id: EmailElementType | undefined) {
    this.activeSubMenuPanel.next(id);
  }
}
