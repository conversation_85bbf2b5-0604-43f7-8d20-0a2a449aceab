<div class="form-group content-connector-setting-form-container" *ngrxLet="elementAttribute$; let attribute">
 
  <div class="email-editor-form-element">
    <div class="email-editor-form-label">
      <unica-typography [variant]="'text'">
        {{ 'EMAIL_EDITOR.LABELS.CC_SETTINGS_INFO' | translate }}
      </unica-typography>
    </div>
    <div class="email-editor-form-label">
      <unica-button
        (clickAction)="addEditExternalContent()"
        [type]="'button'"
        [width]="'180px'">
        {{ (attribute?.options?.contentSourceInfo ? 'EMAIL_EDITOR.LABELS.EDIT_CONTENT' : 'EMAIL_EDITOR.LABELS.LINK_CONTENT') | translate }}
      </unica-button>
    </div>
  </div>
  <div class="form-seperator"></div>

  <element-hide-on-form></element-hide-on-form>

</div>
