import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmailBlock, UnicaStructure, UnicaStructureOptions } from '../../../config/email';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { FocusableEmailElement } from '../../../config/email-element-drop-list';
import {
  ElementBorder,
  ElementPaddingMargin,
  ElementAttribute,
  ElementBackground,
  ElementStyle,
  StyleAttribute,
  HideOnType,
} from '../../../config/email-common-elements';
import { GenerateUniqueId } from '@hcl/unica-common';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { UnicaStructureDropListComponent } from './unica-structure-drop-list/unica-structure-drop-list.component';
import { EmailElementType } from '../../../config/element-toolbar';
import { FOCUSED_STRUCTURE_ACTION_ITEMS } from '../../../helper/email-editor.constant';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'unica-structure',
  standalone: true,
  imports: [
    CommonModule,
    LetDirective,
    FocusOverlayComponent,
    UnicaStructureDropListComponent,
  ],
  providers: [UnicaDialogService], // Add the service as a provider
  templateUrl: './unica-structure.component.html',
  styleUrl: './unica-structure.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UnicaStructureComponent
  implements FocusableEmailElement, AfterViewInit
{
  /**
   * The unique id for this block id
   */
  id: string = GenerateUniqueId(12);
  /**
   * This is the index at which this particular structure fits on the canvas
   */
  @Input() set structureId(n: string) {
    this.id = n + '';
  }

  /**
   * The configuration of the structure
   */
  private struct: UnicaStructure | undefined;
  private structureSubject = new BehaviorSubject<UnicaStructure | undefined>(
    undefined,
  );
  protected structure$ = this.structureSubject.asObservable().pipe(
    tap((s) => {
      this.struct = s;
      if (this.struct) {
        // this.id = this.struct.id + '';
        this.nodeBackgroundSubject.next(this.struct.options.background);
      }
    }),
  );
  @Input() set config(s: UnicaStructure) {
    this.structureSubject.next(s);
  }
  /**
   * Based on the structure Extract the column def from the config, so that we can apply the styles
   */
  protected colDef$ = this.structure$.pipe(
    map((def) => {
      if (def) {
        return def.options.columns ?? [];
      }
      return [];
    }),
  );
  /**
   * The default  background of the entire Structure
   */
  protected nodeBackground: ElementBackground | undefined;
  private nodeBackgroundSubject = new BehaviorSubject<
    ElementBackground | undefined
  >(undefined);
  protected nodeBackground$ = this.nodeBackgroundSubject.asObservable().pipe(
    tap((w) => {
      this.nodeBackground = w;
      if (this.struct?.options) {
        this.struct.options.background = this.nodeBackground;
      }
    }),
  );
  /**
   * The default  border of the entire Structure
   */
  protected nodeBorder: ElementBorder | undefined;
  private nodeBorderSubject = new BehaviorSubject<ElementBorder | undefined>(
    undefined,
  );
  protected nodeBorder$ = this.nodeBorderSubject.asObservable().pipe(
    tap((w) => {
      this.nodeBorder = w;
      if (this.struct?.options) {
        this.struct.options.border = this.nodeBorder;
      }
    }),
  );
  /**
   * The padding for the entire Structure
   */
  protected nodePadding: ElementPaddingMargin | undefined;
  private nodePaddingSubject = new BehaviorSubject<
    ElementPaddingMargin | undefined
  >(undefined);
  protected nodePadding$ = this.nodePaddingSubject.asObservable().pipe(
    tap((w) => {
      this.nodePadding = w;
      if (this.struct?.options) {
        this.struct.options.padding = this.nodePadding;
      }
    }),
  );
  /**
   * The margin for the entire Structure
   */
  protected nodeMargin: ElementPaddingMargin | undefined;
  private nodeMarginSubject = new BehaviorSubject<
    ElementPaddingMargin | undefined
  >(undefined);
  protected nodeMargin$ = this.nodeMarginSubject.asObservable().pipe(
    tap((w) => {
      this.nodeMargin = w;
      if (this.struct?.options) {
        this.struct.options.margin = this.nodeMargin;
      }
    }),
  );

  /*
   * The hide On of this structure
   */
  private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
    undefined,
  );
  protected hideOnSubject$ = this.hideOnSubject.asObservable().pipe(
    tap((w) => {
      if (this.struct?.options) {
        this.struct.options.hideOn = w || undefined;
      }
    }),
  );
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged(),
  );
  /**
   * For structure, we have below toolbar items
   */
  private defaultToolbarItems: ElementToolbarItemConfig[] =
    FOCUSED_STRUCTURE_ACTION_ITEMS;

  protected getDefaultToolbarItems(): ElementToolbarItemConfig[] {
    // If the structure is default designed, then we will show the settings and delete options
    if (this.struct?.options?.isDefaultDesigned) {
      return [{
        id: 'settings',
        icon: 'settings',
        label: 'Settings'
      },
      {
        id: 'delete',
        icon: 'delete',
        label: 'Delete'
      }];
    } else {
      return this.defaultToolbarItems;
    }
  }
  /**
   * For structure, we have below toolbar items
   */
  protected structureToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: 'add',
      icon: 'add',
      label: 'Add Column',
    },
  ];
  /**
   * The default constructor
   */
  constructor(
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private dialogService: UnicaDialogService,
    private translate: TranslateService
  ) {}
  /**
   * Set the focus on canvas
   */
  public focus(event: Event | undefined): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   * Called when a element in the toolbar is selected by the user
   */
  protected performAction({
    item,
    event,
  }: {
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }) {
    switch (item.id) {
      default:
      case 'settings':
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.STRUCTURE_SETTINGS);
        break;
      case 'delete': {
        this.deleteStructure();
        break;
      }
      case 'duplicate': {
        // Get the structure index from the id
        const structureIndex = parseInt(this.id);
        if (!isNaN(structureIndex)) {
          // Duplicate the structure
          this.canvasService.duplicateStructure(structureIndex, true);
        }
        break;
      }
    }
  }

  private async deleteStructure() {
    const confirmed = await this.dialogService.confirm(
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.BLOCK_DELETION_CONFIRMATION'),
      this.translate.instant('MODAL.YES'),
      this.translate.instant('MODAL.NO'),
    );

    if (confirmed) {
      // Get the structure index from the id
      const structureIndex = parseInt(this.id);
      if (!isNaN(structureIndex)) {
        this.canvasService.removeStructureFromCanvas(structureIndex, true);
      }
    }
  }

  /**
   * Return the Story that is applied to this structure
   */
  public getStyle(): ElementStyle {
    const options = this.struct?.options;
    if (options) {
      return options;
    }
    return {};
  }

  /**
   * Get the columns Width
   */
  protected getColumnWidth(
    opt: UnicaStructureOptions | undefined,
    index: number,
  ): string {
    if (opt) {
      return opt.columnsWidth && opt.columnsWidth.length > index
        ? opt.columnsWidth[index] * (opt.columnsWidth.length === 1 ? 100 : 10) +
            '%'
        : '';
    }
    return '';
  }

  /**
   * Get the elements for a specific column
   */
  protected getColumnElements(structure: UnicaStructure | null, columnIndex: number): EmailBlock[] {
    if (!structure?.elements || structure.elements.length <= columnIndex) {
      return [];
    }
    return structure.elements[columnIndex] || [];
  }

  /**
   * Update the style of the Structure
   */
  public updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'background' && value && value.background) {
      this.nodeBackgroundSubject.next(<ElementBackground>value.background);
    } else if (attribute === 'border' && value && value.border) {
      this.nodeBorderSubject.next(value.border);
    } else if (attribute === 'padding' && value && value.padding) {
      this.nodePaddingSubject.next(<ElementPaddingMargin>value.padding);
    } else if (attribute === 'margin' && value && value.margin) {
      this.nodePaddingSubject.next(<ElementPaddingMargin>value.margin);
    } else if (attribute === 'hideOn' && value && value.hideOn !== undefined) {
      this.hideOnSubject.next(<HideOnType>value.hideOn);
    }
  }

  /**
   * Once the elements is dropped we will set the focus on this
   */
  ngAfterViewInit(): void {
    this.canvasService.setFocus(this);
  }

  getElementAttribute(): ElementAttribute | undefined {
    return undefined;
  }

  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {}
}
