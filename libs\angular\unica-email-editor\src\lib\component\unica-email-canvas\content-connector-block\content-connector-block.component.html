<focus-overlay
  #focusOverLay
  (click)="focus($event)"
  *ngrxLet="style$; let style"
  [items]="buttonToolbarItems"
  [draggable]="true"
  [invertedDraggable]="true"
  [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
  [hoverCss]="'element-hover-element'"
  (select)="performAction($event)"
  [isActive]="(isFocused$ | async) ?? false" style="position: relative;">
  <div *ngrxLet="optionsSubject$; let options" class="external-content-container">
    <ng-template #noContentSource>
      <div class="external-content-label">
        <unica-typography [variant]="'text'">
          {{ 'EMAIL_EDITOR.LABELS.EXTERNAL_CONTENT' | translate }}
        </unica-typography>
      </div>
    </ng-template>
    <ng-container *ngIf="options?.contentSourceInfo; else noContentSource">
      <div class="external-content-details">
        <div>{{ 'EMAIL_EDITOR.LABELS.EXTERNAL_CONTENT_SOURCE' | translate }}:</div> 
        <div>
          {{ options?.contentSourceInfo?.url }}
        </div> 
      </div>
    </ng-container>
  </div>
</focus-overlay>