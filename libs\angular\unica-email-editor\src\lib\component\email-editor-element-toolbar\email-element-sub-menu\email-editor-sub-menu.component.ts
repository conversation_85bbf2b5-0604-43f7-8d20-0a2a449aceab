import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UnicaButtonToggleGroupComponent } from '@hcl/angular/unica-button';
import { UnicaTemplateDirective } from '@hcl/angular/unica-angular-common';
import { ElementStyleFormComponent } from '../../forms/element-style-form/element-style-form.component';
import { EmailElementType } from '../../../config/element-toolbar';
import { BehaviorSubject, distinctUntilChanged, map } from 'rxjs';
import { ElementSettingsComponent } from './element-settings/element-settings.component';
import { FontListingComponent } from './font-listing/font-listing.component';
import { StructureListingComponent } from './structure-listing/structure-listing.component';
import { SpacerListingComponent } from './spacer-listing/spacer-listing.component';
import { ImageSettingsComponent } from './image-settings/image-settings.component';
import { ColumnSettingsComponent } from './column-settings/column-settings.component';
import { CanvasSettingsComponent } from './canvas-settings/canvas-settings.component';
import { ButtonSettingsComponent } from './button-settings/button-settings.component';
import { DividerSettingsComponent } from './divider-settings/divider-settings.component';
import { SpacerSettingsComponent } from './spacer-settings/spacer-settings.component';
import { StructureSettingsComponent } from './structure-settings/structure-settings.component';
import { TextSettingsComponent } from './text-settings/text-settings.component';
import { WebpageSettingsComponent } from './webpage-settings/webpage-settings.component';
import { PreferencesUnsubscribeSettingsComponent } from './preferences-unsubscribe-settings/preferences-unsubscribe-settings.component';
import { ContentConnectorSettingsComponent } from './content-connector-settings/content-connector-settings.component';

@Component({
  selector: 'email-editor-sub-menu',
  standalone: true,
  imports: [CommonModule, UnicaTypographyComponent, UnicaButtonToggleGroupComponent,
    UnicaTemplateDirective, ElementStyleFormComponent, ElementSettingsComponent, FontListingComponent,
    StructureListingComponent, SpacerListingComponent, ImageSettingsComponent, ButtonSettingsComponent, 
    DividerSettingsComponent, SpacerSettingsComponent, ColumnSettingsComponent, CanvasSettingsComponent, 
    StructureSettingsComponent, TextSettingsComponent, PreferencesUnsubscribeSettingsComponent, WebpageSettingsComponent, ContentConnectorSettingsComponent],
  templateUrl: './email-editor-sub-menu.component.html',
  styleUrl: './email-editor-sub-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EmailEditorSubMenuComponent {
  /**
   * The title of the sub-menu
   */
  private typeSubject = new BehaviorSubject<EmailElementType | undefined>(undefined);
  protected type$ = this.typeSubject.asObservable().pipe(distinctUntilChanged());
  @Input() set type(t: EmailElementType | undefined) {
    this.typeSubject.next(t);
  }
  /**
   * The title of the panel
   */
  protected title$ = this.type$.pipe(
    map((t) => {
      switch (t) {
        default:
        case EmailElementType.IMAGE_SETTINGS:
          return 'Image Settings';
        case EmailElementType.BUTTON_SETTINGS:
            return 'Button Settings';
        case EmailElementType.SPACER_SETTINGS:
          return 'Spacer Settings';
        case EmailElementType.STRUCTURE_SETTINGS:
          return 'Structure Settings';
        case EmailElementType.DIVIDER_SETTINGS:
          return 'Divider Settings';
        case EmailElementType.WEBPAGE_SETTINGS:
          return 'Webpage Settings';
        case EmailElementType.PREFERENCES_UNSUBSCRIBE_SETTINGS:
          return 'Mangage Preferences Settings';
        case EmailElementType.CANVAS_SETTINGS:
          return 'Canvas Settings';
        case EmailElementType.COLUMN_SETTINGS:
          return 'Column Settings';
        case EmailElementType.STRUCTURE:
          return 'Layout Modules';
        case EmailElementType.TEXT:
          return 'Fonts';
        case EmailElementType.SPACER:
          return 'Spacer';
        case EmailElementType.TEXT_SETTINGS:
          return 'Text Settings';
        case EmailElementType.CONTENT_CONNECTOR_SETTINGS:
          return 'External Content Settings';
      }
    })
  );
  /**
   * Required to access the enum in the template html
   * @protected
   */
  protected readonly EmailElementType = EmailElementType;
}
