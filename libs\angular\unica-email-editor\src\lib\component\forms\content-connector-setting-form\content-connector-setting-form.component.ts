import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LetDirective } from '@ngrx/component';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { UnicaTypographyComponent } from '@hcl/angular/unica-typography';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { ElementHideOnFormComponent } from '../element-hide-on-form/element-hide-on-form.component';
import { UnicaButtonModule } from '@hcl/angular/unica-button';

@UntilDestroy()
@Component({
  selector: 'content-connector-setting-form',
  standalone: true,
  imports: [CommonModule, TranslatePipe, UnicaTypographyComponent, UnicaButtonModule, ElementHideOnFormComponent, LetDirective],
  templateUrl: './content-connector-setting-form.component.html',
  styleUrl: './content-connector-setting-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ContentConnectorSettingFormComponent {
  /**
   * Get the style of the current element
   */
  protected elementStyle$ = this.canvasService.styleOfFocusedElement$
  /**
   * Get the attribute of the current element
   */
  protected elementAttribute$ = this.canvasService.attributeOfFocusedElement$
 
  /**
   * Default constructor
   */
  constructor(private canvasService: EmailCanvasService, 
    private translate: TranslateService) {
  }

  addEditExternalContent() {
    this.canvasService.openLinkContentConnector();
  }
}
