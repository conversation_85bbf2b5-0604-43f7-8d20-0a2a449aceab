import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FocusableEmailElement } from '../../../config/email-element-drop-list';
import { GenerateUniqueId } from '@hcl/unica-common';
import { ElementToolbarItemConfig } from '../../../config/email-canvas';
import { EmailDefaultService } from '../../../service/email-default.service';
import { EmailCanvasService } from '../../../service/email-canvas.service';
import { EmailElementToolbarService } from '../../../service/email-element-toolbar.service';
import { EmailElementType } from '../../../config/element-toolbar';
import {
  ElementAttribute,
  ElementStyle,
  ElementWidthHeight,
  HideOnType,
  StyleAttribute,
} from '../../../config/email-common-elements';
import { BehaviorSubject, distinctUntilChanged, map, tap } from 'rxjs';
import { LetDirective } from '@ngrx/component';
import { ElementHeightDirective } from '../../../directive/element-height.directive';
import { FocusOverlayComponent } from '../focus-overlay/focus-overlay.component';
import { EmailBlock, UnicaSpacerBlock } from '../../../config/email';
import { BlockActionsService } from '../../../service/block-actions.service';
import { UnicaDialogService } from '@hcl/angular/unica-dialog';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'spacer-block',
  standalone: true,
  imports: [
    CommonModule,
    LetDirective,
    ElementHeightDirective,
    FocusOverlayComponent,
  ],
  templateUrl: './spacer-block.component.html',
  styleUrl: './spacer-block.component.scss',
})
export class SpacerBlockComponent implements FocusableEmailElement {
  /**
   * The unique id
   */
  private _id: string = GenerateUniqueId(13);
  private _dropListId = '';
  private _blockIndex = 0;

  get id(): string {
    return this._dropListId
      ? `${this._dropListId}_${this._blockIndex}`
      : this._id;
  }

  @Input() set dropListId(value: string) {
    this._dropListId = value;
  }

  @Input() set blockIndex(value: number) {
    this._blockIndex = value;
  }
  /**
   * The input for the block
   */
  private _block = new UnicaSpacerBlock();
  private blockSubject = new BehaviorSubject<UnicaSpacerBlock>(this._block);
  protected block$ = this.blockSubject
    .asObservable()
    .pipe(tap((b) => (this._block = b)));
  @Input({
    transform: (value: EmailBlock): UnicaSpacerBlock => <UnicaSpacerBlock>value,
  })
  set block(b: UnicaSpacerBlock) {
    this.blockSubject.next(b);
    this.heightSubject.next(b.options.height);
    this.hideOnSubject.next(b.options.hideOn);
  }
  /**
   * The style
   * @protected
   */
  protected style$ = this.block$.pipe(
    map((b) => {
      if (b) {
        return b.options;
      }
      return undefined;
    }),
  );

  /**
   * For canvas, Whe it is empty we have below actions
   */
  protected spacerToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings',
    },
    {
      id: 'duplicate',
      icon: 'content_copy',
      label: 'Duplicate',
    },
    {
      id: 'delete',
      icon: 'delete',
      label: 'Delete',
    },
  ];
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged(),
  );
  /**
   *
   * @param defaultService
   */
  constructor(
    private defaultService: EmailDefaultService,
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    private blockActionsService: BlockActionsService,
    private dialogService: UnicaDialogService,
    private translate: TranslateService
  ) {}
  /**
   * Set the focus on canvas, this is called when the user clicks on
   * any empty location on the canvas
   */
  public focus(event?: Event): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   * Called when a element in the toolbar is selected by the user
   */
  protected performAction({
    item,
    event,
  }: {
    item: ElementToolbarItemConfig;
    event: MouseEvent | KeyboardEvent;
  }) {
    switch (item.id) {
      default:
      case 'settings':
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.SPACER_SETTINGS);
        break;
      case 'duplicate': {
        this.blockActionsService.duplicateBlock(this);
        break;
      }
      case 'delete': {
        this.deleteBlock();
        break;
      }
    }
  }
  private async deleteBlock() {
    const confirmed = await this.dialogService.confirm(
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.CONFIRM_DELETION'),
      this.translate.instant('EMAIL_EDITOR.LABELS.MODAL_MESSAGE.BLOCK_DELETION_CONFIRMATION'),
      this.translate.instant('MODAL.YES'),
      this.translate.instant('MODAL.NO'),
    );

    if (confirmed) {
      this.blockActionsService.deleteBlock(this);
    }
  }
  /**
   * Get the style of this button
   */
  getStyle(): ElementStyle {
    return this._block.options;
  }

  updateStyle<K extends ElementStyle>(
    attribute: StyleAttribute,
    value: K | undefined,
  ): void {
    if (attribute === 'height' && value && value.height) {
      this.heightSubject.next(<ElementWidthHeight>value.height);
    } else if (attribute === 'hideOn' && value && value.hideOn !== undefined) {
      // we are checking for undefined because hideOn can be empty string also
      this.hideOnSubject.next(<HideOnType>value.hideOn);
    }
  }
  getElementAttribute(): ElementAttribute | undefined {
    return undefined;
  }

  updateElementAttribute<K extends ElementAttribute>(
    attribute: string,
    value: K | undefined,
  ): void {}

  /**
   * The height of the block
   */
  private heightSubject = new BehaviorSubject<ElementWidthHeight | undefined>(
    undefined,
  );
  protected heightSubject$ = this.heightSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.height = w;
      }
    }),
  );

  /*
   * The hide On of this block
   */
  private hideOnSubject = new BehaviorSubject<HideOnType | undefined>(
    undefined,
  );
  protected hideOnSubject$ = this.hideOnSubject.asObservable().pipe(
    tap((w) => {
      if (this._block?.options) {
        this._block.options.hideOn = w || undefined;
      }
    }),
  );
}
