import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ElementStyleFormComponent } from '../../../forms/element-style-form/element-style-form.component';
import { ContentConnectorSettingFormComponent } from '../../../forms/content-connector-setting-form/content-connector-setting-form.component';

@Component({
  selector: 'content-connector-settings',
  standalone: true,
  imports: [CommonModule, ElementStyleFormComponent, ContentConnectorSettingFormComponent],
  templateUrl: './content-connector-settings.component.html',
  styleUrl: './content-connector-settings.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ContentConnectorSettingsComponent {
}
