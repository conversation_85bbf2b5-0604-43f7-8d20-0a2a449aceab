import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  Renderer2,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er<PERSON>ontainer,
  MatDrawerContent,
} from '@angular/material/sidenav';

import { EmailEditorElementToolbarComponent } from '../email-editor-element-toolbar/email-editor-element-toolbar.component';
import { EmailHistoryImplService } from '../../service/email-history-impl.service';
import { EmailHistoryService } from '../../config/email-history';
import { UnicaEmailCanvasComponent } from '../unica-email-canvas/unica-email-canvas.component';
import { EmailCanvasService } from '../../service/email-canvas.service';
import { EmailElementToolbarService } from '../../service/email-element-toolbar.service';
import { EmailDropListRegistryService } from '../../service/email-drop-list-registry.service';
import { UnicaEmail } from '../../config/email';
import { EmailSubMenuService } from '../../service/email-sub-menu.service';
import { LetDirective } from '@ngrx/component';
import { CdkConnectedOverlay, ConnectedPosition } from '@angular/cdk/overlay';
import { EmailEditorSubMenuComponent } from '../email-editor-element-toolbar/email-element-sub-menu/email-editor-sub-menu.component';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { IDynamicContentData } from '../../config/email-common-elements';
import { TranslateModule } from '@ngx-translate/core';

/**
 * this is the unica email editor
 */
@UntilDestroy()
@Component({
  selector: 'unica-email-editor',
  standalone: true,
  imports: [
    CommonModule,
    MatDrawerContainer,
    MatDrawer,
    MatDrawerContent,
    EmailEditorElementToolbarComponent,
    UnicaEmailCanvasComponent,
    LetDirective,
    CdkConnectedOverlay,
    EmailEditorSubMenuComponent,
    TranslateModule,
  ],
  providers: [
    EmailHistoryImplService,
    EmailCanvasService,
    EmailElementToolbarService,
    EmailDropListRegistryService,
    EmailSubMenuService,
  ],
  templateUrl: './unica-email-editor.component.html',
  styleUrl: './unica-email-editor.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UnicaEmailEditorComponent implements OnInit {
  /**
   * Base on this flag we can position the elements toolbar
   * to the left or right, by default it is 'right'
   */
  @Input() elementToolbarPosition: 'left' | 'right' = 'right';

  /**
   * Flag to track if the toolbar is being dragged
   */
  public isDraggingToolbar = false;
  /**
   * Set the email that needs to be rendered
   * @param e
   */
  @Input() set email(e: UnicaEmail) {
    this.canvasService.setEmail(e);
  }
  /**
   * When the editor is ready we will emit this event which will
   * provide the user with various services that user can use
   */
  @Output() init = new EventEmitter<{
    historyService: EmailHistoryService;
    canvasService: EmailCanvasService;
  }>();

  /**
   * Output event emitter to open the rule builder screen
   * this for adding dynamic content to button / image / links
   */
  @Output() openRuleBuilder = new EventEmitter<IDynamicContentData>();
  /**
   * Output event emitter to open the content picker screen
   * this for adding internal image to image block or may be used later to add
   * background images to strcutures as well
   */
  @Output() openContentPicker = new EventEmitter<null>();
  /**
   * Output event emitter to open the content connector screen
   * this for adding external content block to the email
   */
  @Output() openContentConnector = new EventEmitter<null>();
  /**
   * In some cases we will have to open the overlay with teh elements directly
   * here, so we have to decide the position strategy
   */
  protected overlayPosition: ConnectedPosition[] = [
    // this is when the toolbar is on the right
    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },
    // this is when the toolbar is on the left
    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },
  ];
  /**
   * Now it may happen that user has clicked outside the overlay
   * in such cases we will have to close it, this function will identify where the user has clicked
   * & then close the overlay
   */
  private verifyAndCloseOverlay = (event: MouseEvent) => {
    let clickedInside = false;
    let target = event.target as HTMLElement;
    while (target != null) {
      if (target.tagName === 'BODY') {
        break;
      }
      if (target.classList.contains('editor-sub-menu-container')) {
        clickedInside = true;
        break;
      }
      target = target.parentNode as HTMLElement;
    }
    if (!clickedInside) {
      this.closeOverlay();
    }
  };
  /**
   * The default constructor
   * @param historyService
   */
  constructor(
    private historyService: EmailHistoryImplService,
    private canvasService: EmailCanvasService,
    private renderer: Renderer2,
    protected toolbarService: EmailElementToolbarService,
    protected subMenuService: EmailSubMenuService,
    private cdr: ChangeDetectorRef,
  ) {
    this.toolbarService.dragStart$.pipe(untilDestroyed(this)).subscribe((s) => {
      if (!s) {
        this.closeOverlay();
      }
    });

    this.canvasService.dynamicContentInfo$
      .pipe(untilDestroyed(this))
      .subscribe((ruleInfo) => {
        this.openRuleBuilder.emit(ruleInfo);
      });
    this.canvasService.openContentManager$
      .pipe(untilDestroyed(this))
      .subscribe(() => {
        this.openContentPicker.emit();
      });
  }
  /**
   * When ever the overlay is opened we will look for any outside click
   * so that we can close the overlay if the user clicks anywhere outside
   */
  protected onOverlayAttach(): void {
    setTimeout(() => {
      document.addEventListener('click', this.verifyAndCloseOverlay, true);
      // this is added as the toolbar of the focus will overlap this ovelay
      const subMenu = document.querySelector('.element-toolbar-sub-menu');
      if (subMenu) {
        const parent = subMenu.parentNode?.parentNode;
        if (parent) {
          this.renderer.setStyle(parent, 'z-index', '1001');
        }
      }
    });
  }
  /**
   * When the overlay is closed lets not listen to any outside clicks as this
   * will be over head
   */
  protected onOverlayDetach(): void {
    document.removeEventListener('click', this.verifyAndCloseOverlay, true);
    this.closeOverlay();
  }
  /**
   * This function will close the overlay
   */
  protected closeOverlay() {
    this.subMenuService.activateSubMenu(undefined);
  }
  /**
   * Fire the init event
   */
  ngOnInit(): void {
    this.init.emit({
      historyService: this.historyService,
      canvasService: this.canvasService,
    });
  }

  /**
   * Handle toolbar position change from child component
   * @param newPosition - The new position for the toolbar
   */
  protected onToolbarPositionChange(newPosition: 'left' | 'right'): void {
    this.elementToolbarPosition = newPosition;
  }

  /**
   * Handle toolbar drag state change from child component
   * @param isDragging - Whether the toolbar is being dragged
   */
  protected onToolbarDragStateChange(isDragging: boolean): void {
    this.isDraggingToolbar = isDragging;
  }

  /**
   * Handle drag over event for drop zones
   * @param event - The drag over event
   * @param position - The target position
   */
  protected onDropZoneDragOver(
    event: DragEvent,
    position: 'left' | 'right',
  ): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  /**
   * Handle drop event for drop zones
   * @param event - The drop event
   * @param position - The target position
   */
  protected onDropZoneDrop(event: DragEvent, position: 'left' | 'right'): void {
    event.preventDefault();
    this.elementToolbarPosition = position;
    this.isDraggingToolbar = false;
    this.cdr.detectChanges();
  }
}
