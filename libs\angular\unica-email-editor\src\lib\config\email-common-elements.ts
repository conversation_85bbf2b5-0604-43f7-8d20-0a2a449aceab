import { EmailBlock, UnicaContentConnectorBlockOptions } from "./email";

/**
 * The unit of measurement for
 */
export type UnitOfMeasurement = '%' | 'px' | 'cover' | 'contain';
/**
 * Which direction do we support left to right 'ltr' or right to left 'rtl'
 */
export type FlowDirection = 'ltr' | 'rtl';
/**
 * The Font line height
 */
export type ElementLineHeightUnit = 'px' | 'none';
/**
 * This is the type for the background
 */
export type BackgroundRepeat =
  | 'no-repeat'
  | 'repeat'
  | 'repeat-x'
  | 'repeat-y';
/**
 * this unit defines the vertical alignment of an entity
 */
export type VerticalAlign = 'top' | 'middle' | 'bottom';
/**
 * The unit for alignment
 */
export type Align = 'left' | 'center' | 'right';
/**
 * how do we plan to open the popup
 */
export type LinkTarget = '_blank' | '_self' | '_parent' | '_top';
/**
 * The style of the font
 */
export type FontStyle = 'italic' | 'normal' | 'oblique';
/**
 * The weight of the font
 */
export type FontWeight =
  | number
  | 'bold'
  | 'bolder'
  | 'inherit'
  | 'initial'
  | 'light'
  | 'normal';
/**
 * This is the type of hide-on which can be on any block or structure
 */
export type HideOnType = 'desktop' | 'mobile';

/**
 * This is the type of font family that we support
 * in the email editor
 */
export type FontFamily = Set<string>;
/**
 * This is the interface that holds the border that is associated to
 * any element that is present ion the email canvas
 */
export interface ElementBorder {
  color?: string;
  style?: 'solid' | 'dotted' | 'dashed' | 'double' | 'groove';
  width?: number;
  widthTop?:number;
  widthBottom?:number;
  widthLeft?:number;
  widthRight?:number;
  radius?: number;
  radiusTopLeft?: number;
  radiusTopRight?: number;
  radiusBottomLeft?: number;
  radiusBottomRight?: number;
}
/**
 * This is the interface that holds the Dimensions that is associated to any
 * element that is present ion the email canvas.
 */
export interface ElementWidthHeight {
  value: number;
  unit: UnitOfMeasurement;
  auto?: boolean;
  units?: UnitOfMeasurement[];
}
/**
 * This is the interface that can hold th epadding or margin
 * provided to any element on the email canvas
 */
export interface ElementPaddingMargin {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
}
/**
 * This interface defines the background of an element
 */
export interface ElementBackground {
  color?: string;
  url?: string;
  repeat?: BackgroundRepeat;
  size?: ElementWidthHeight;
  width?: ElementWidthHeight;
  height?: ElementWidthHeight;
  isTransparent?: boolean;
}
/**
 * This interface holds the details of Link associated
 * to any element
 */
export interface ElementLink {
  href: string;
  target: LinkTarget;
  id?: string;
}
/**
 * This interfaces holds the details of the image
 */
export type ElementImage = {
  width?: ElementWidthHeight;
  height?: ElementWidthHeight;
  src?: string;
}
/**
 * The line height
 */
export interface ElementLineHeight {
  value?: number;
  unit?: ElementLineHeightUnit;
}
/**
 * This interface defines how the fonts
 * are rendered on the particular element
 */
export interface ElementFont {
  family?: string;
  fallback?: string;
  size?: number;
  style?: FontStyle;
  weight?: FontWeight;
}

/**
 * Object that wraps most of the style attributes
 */
export interface ElementStyle {
  width?: ElementWidthHeight;
  background?:ElementBackground;
  border?: ElementBorder;
  padding?: ElementPaddingMargin;
  margin?: ElementPaddingMargin;
  font?: ElementFont;
  height?: ElementWidthHeight;
  verticalAlign?: VerticalAlign;
  lineHeight?: ElementLineHeight;
  fullWidth?: boolean;
  align?: Align;
  color?: string;
  backgroundColor?: string;
  hideOn?: HideOnType | undefined;
}
/**
 * The weight of the font
 */
export type StyleAttribute =
  'width' | 'background' | 'padding' | 'border' | 'font' | 'margin' | 'height' | 'color' | 'lineHeight' | 'fullWidth' | 'hideOn'  | 'align' | 'verticalAlign'

export interface ElementAttribute {
  innerText?: string;
  rules?: IRule[];
  src?: string;
  downloadDetails?: IDownloadDetailInfo | null;
  options?: UnicaContentConnectorBlockOptions;
}

export interface IRule {
  id?: string;
  name: string;
  buttonText?: string;
  redirection?: 'url' | 'lp';
  url?: string;
  landingPage?: string;
  isDefault?: boolean;
  ruleJson: string;
  aliansName?: string;
  linkId?: string;
  startIndex?: number;
  endIndex?: number;
  linkText?: string;
  imageSrc?: string;
  digitalAssetId?: Number;
}

/**
 * Common interface for dynamic content data to be 
 * used for integration with rule builder screen in old editor
 */
export interface IDynamicContentData {
  ruleInfo?: IRule;
  currentElement: EmailBlock | undefined;
  hyperlinkInfo?: {
    id?: string;
    startIndex?: number;
    endIndex?: number;
    linkText?: string;
  };
}

/**
 * Defines the data structure for the Add Link Popover form.
 */
export interface AddLinkFormDataConfig {
  redirection: 'url' | 'lp';
  url: string;
  landingPage: string;
  aliasNameInfo: {
    name: string;
    id: string;
  };
  newWindow: boolean;
}
export interface IDownloadDetailInfo {
  // the Id of the asset that we want to download
  assetId?: number | string,
  // the id that needs to be given to the tag
  id?: string,
  // the URL which needs to be hit to download the asset
  assetUrl?: string,
  // flag thatr tells the current status of the asset
  assetDownloaded?: boolean
}



