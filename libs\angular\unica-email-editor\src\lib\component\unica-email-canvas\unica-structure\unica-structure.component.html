<!--This is the canvas on which the global settings will be applied-->
<div style="width: 100%; height: 100%;display: flex"
     tabindex="0"
     (click)="focus($event)"
     (keydown)="focus($event)">

  <focus-overlay *ngrxLet="structure$; let structure"
    style="width: 100%;position: relative;"
    #focusOverLay
    [items]="getDefaultToolbarItems()"
    [structureToolbarItems]="structureToolbarItems"
    [nodeHideOn]="(hideOnSubject$ | async) ?? undefined"
    [nodeBorder]="(nodeBorder$ | async) ?? null"
    [nodeBackground]="(nodeBackground$ | async) ?? null"
    [nodePadding]="(nodePadding$ | async) ?? null"
    [hoverCss]="'structure-hover-element'"
    [isActive]="(isFocused$ | async) ?? false"
    (select)="performAction($event)"
    [draggable]="!structure?.options?.isDefaultDesigned">
    <div class="structure-container"
         style="gap: 4px">
      <!--We have the columns in UnicaStructureOptions.columns & the elements in the column are elements-->
      <ng-container *ngFor="let element of ((colDef$ | async) ?? []); let i = index">
        <!--We cannot create a drop list here directly as we have to register it to drop registry, so we have a component-->
        <unica-structure-drop-list
          *ngIf="structure && structure.elements"
          [dropListId]="id + ':' + i"
          [ngStyle]="{width: getColumnWidth(structure?.options, i)}"
          [columnConfig]="element"
          [elements]="getColumnElements(structure, i)"
        >
        </unica-structure-drop-list>
      </ng-container>
    </div>
  </focus-overlay>
</div>
