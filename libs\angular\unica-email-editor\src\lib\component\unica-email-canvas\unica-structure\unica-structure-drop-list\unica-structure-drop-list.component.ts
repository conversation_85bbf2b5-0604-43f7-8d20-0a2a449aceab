import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, distinctUntilChanged, filter, map, tap } from 'rxjs';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDragMove,
  CdkDragPlaceholder,
  CdkDragPreview,
  CdkDragRelease,
  CdkDropList
} from '@angular/cdk/drag-drop';
import {
  EmailBlock,
  UnicaImageBlock,
  UnicaStructureColumn
} from '../../../../config/email';
import { LetDirective } from '@ngrx/component';
import { FocusOverlayComponent } from '../../focus-overlay/focus-overlay.component';
import { EmailCanvasService } from '../../../../service/email-canvas.service';
import { EmailElementDropList, FocusableEmailElement } from '../../../../config/email-element-drop-list';
import { ElementAttribute, ElementStyle, StyleAttribute, VerticalAlign } from '../../../../config/email-common-elements';
import { GenerateUniqueId } from '@hcl/unica-common';
import { ElementToolbarItemConfig } from '../../../../config/email-canvas';
import { EmailElementToolbarService } from '../../../../service/email-element-toolbar.service';
import { EmailDropListRegistryService } from '../../../../service/email-drop-list-registry.service';
import { EmptyElementComponent } from '../../empty-element/empty-element.component';
import { TextBlockComponent } from '../../text-block/text-block.component';
import { ImageBlockComponent } from '../../image-block/image-block.component';
import { EmailElementType } from '../../../../config/element-toolbar';
import { ButtonBlockComponent } from '../../button-block/button-block.component';
import { DragPreviewComponent } from '../../drag-preview/drag-preview.component';
import { SpacerBlockComponent } from '../../spacer-block/spacer-block.component';
import { DividerBlockComponent } from '../../divider-block/divider-block.component';
import {
  COLUMN_SETTINGS,
  ELEMENT_TOOLBAR_ID,
  TOOLBAR_IMAGE
} from '../../../../helper/email-editor.constant';
import { WebpageBlockComponent } from '../../webpage-block/webpage-block.component';
import { PreferencesUnsubscribeBlockComponent } from '../../preferences-unsubscribe-block/preferences-unsubscribe-block.component';
import { VerticalAlignmentDirective } from '../../../../directive/vertical-alignment.directive';
import { BlockActionsService } from '../../../../service/block-actions.service';
import { ContentConnectorBlockComponent } from '../../content-connector-block/content-connector-block.component';

@Component({
  selector: 'unica-structure-drop-list',
  standalone: true,
  imports: [CommonModule, CdkDropList, LetDirective, FocusOverlayComponent, EmptyElementComponent, CdkDrag, TextBlockComponent, ImageBlockComponent, ButtonBlockComponent, SpacerBlockComponent, DividerBlockComponent, WebpageBlockComponent, PreferencesUnsubscribeBlockComponent, ContentConnectorBlockComponent, DragPreviewComponent, CdkDragPreview, CdkDragPlaceholder, VerticalAlignmentDirective],
  providers: [BlockActionsService],
  templateUrl: './unica-structure-drop-list.component.html',
  styleUrl: './unica-structure-drop-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UnicaStructureDropListComponent implements FocusableEmailElement, EmailElementDropList, AfterViewInit {

  /**
   * Input for element vertical alignment (for host-level directive binding)
   */
  private verticalAlignSubject = new BehaviorSubject<VerticalAlign | undefined>( undefined);
  protected elementVerticalAlign$ = this.verticalAlignSubject.asObservable().pipe(
    tap(v => {
      if (this._column) {
        this._column.verticalAlign = v || undefined;
      }
    }),
  );
  /**
   * The unique Id
   */
  id: string = GenerateUniqueId(12);
  /**
   * This is the index at which this particular structure fits on the canvas
   */
  @Input() set dropListId(n: string) {
    // TODO: id has been changed so we may have to re-register
    this.id = n + '';
    this.dropListRegistry.register(this);
  }
  /**
   * The list of elements in the Structure
   * @param e
   */
  private _elements: EmailBlock[] =[];
  private elementsSubject = new BehaviorSubject<EmailBlock[]>([]);
  protected elements$ = this.elementsSubject.asObservable()
      .pipe(
        tap((l) => this._elements = l)
      );
  @Input() set elements(e: EmailBlock[] | undefined){
    if (!e) {
      this.elementsSubject.next([])
    } else {
      this.elementsSubject.next(e);
    }
  }
  /**
   * Refresh the Drop list
   */
  public refresh$ = this.canvasService.refreshDropList$.pipe(
    map((id) => id && id === this.id ? true : false)
  );
  /**
   * Set teh column Def for this drop List
   */
  private _column: UnicaStructureColumn | undefined;
  private columnSubject = new BehaviorSubject<UnicaStructureColumn | undefined>(undefined);
  protected column$ = this.columnSubject.asObservable()
    .pipe(
      filter(c => !!c),
      tap((l) => {
        this._column = l;
        // No direct style application here; handled reactively via updateStyle
      })
    );
  @Input() set columnConfig(c: UnicaStructureColumn) {
    this.columnSubject.next(c);
  }
  /**
   * The drop list for this, we will modify the array such that we remove the drop list with
   * ID
   * @protected
   */
  protected structureDropList$ = this.dropListRegistry.dropLists$.pipe(
    map((lst) => {
      return lst.filter((cdk) => cdk.id !== 'layout-listing');
    })
  );
  /**
   * For canvas, Whe it is empty we have below actions
   */
  protected emptyStructureItems: ElementToolbarItemConfig[] = [
    {
      id: 'image',
      icon: 'unica_image_module',
      label: 'Add Image'
    },
    {
      id: 'text',
      icon: 'unica_text_module',
      label: 'Add Text'
    }
  ];
  /**
   *
   */
  @ViewChild('container') container !: CdkDropList
  /**
   * This is a variable that will tell if there is any element
   * being dropped over this block
   * @protected
   */
  protected isElementBeingDropped$ = this.dropListRegistry.currentActiveDropList$.pipe(
    map((x) =>
      x && this.container && x === this.container.id
    )
  );
  /**
   * For structure, we have below toolbar items
   */
  protected columnToolbarItems: ElementToolbarItemConfig[] = [
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings'
    }
  ];
  /**
   * This tells when the canvas is in focus, when there is nothing in focus
   * then canvas will be by default be in focus
   */
  protected isFocused$ = this.canvasService.focus$.pipe(
    map((b) => !b || b.id === this.id),
    distinctUntilChanged()
  );
  /**
   * Default constructor
   */
  constructor(
    protected canvasService: EmailCanvasService,
    private toolbarService: EmailElementToolbarService,
    protected dropListRegistry: EmailDropListRegistryService,
    private cdr: ChangeDetectorRef
  ) {
    this.refresh$.subscribe((flg) => {
      if (flg) {
        this.cdr.detectChanges();
      }
    });
  }
  /**
   * Set the focus on canvas
   */
  public focus(event: Event | undefined): void {
    if (event) {
      // stop the bubble
      event.stopPropagation();
    }
    this.canvasService.setFocus(this);
  }
  /**
   * This verifies if an element can be dropped on this editor.
   * This internally calls the drop reg as this registry can tell which is the
   * current drop-list that is active
   */
  protected canElementBeDropped(drag: CdkDrag, drop: CdkDropList): boolean {
    let bool = false
    const data: {type: string} = drag.data;
    if (data && data.type.startsWith('cols_') || data.type === 'custom_structure') {
      // structure cannot be dropped in structure
      bool =  false;
    }else {
      bool = this.dropListRegistry.isDropAllowed(drag, drop);
    }
    return bool;
  };
  /**
   * This function will generate the Drag data
   */
  protected generateDragData(block: EmailBlock, index: number): { dropListId: string, index: number, block: EmailBlock, type: string} {
    return {
      dropListId: this.id,
      index: index,
      type: block.type ?? 'text',
      block: block
    }
  }
  /**
   * Called when a element in the toolbar is selected by the user
   */
  public performAction({ item, event }: { item: ElementToolbarItemConfig, event: MouseEvent | KeyboardEvent}) {
    switch (item.id) {
      default:
      case COLUMN_SETTINGS:
        // fire event to open the settings panel
        this.toolbarService.openPanel(EmailElementType.COLUMN_SETTINGS);
        break;
      case TOOLBAR_IMAGE:
        // fire event to add a image on the canvas
        this.canvasService.addBlockToDropList(this.id, 0, new UnicaImageBlock());
        break;
    }
  }
  /**
   * Lifecycle hook to handle changes to inputs
   */
  ngAfterViewInit(): void {
    // register this drop list
    this.dropListRegistry.register(this);
  }

  /**
   * Return the instance of the CDK Drop list
   */
  public getDropList(): CdkDropList {
    return this.container;
  }
  /**
   * Tet the styles
   */
  public getStyle(): ElementStyle {
    return <ElementStyle>this._column;
  }

  /**
   * Update the style of the drop list, including vertical alignment
   */
  public updateStyle<K extends ElementStyle>(attribute: StyleAttribute, value: K | undefined): void {
    if (attribute === 'verticalAlign' && value && value.verticalAlign !== undefined) {
      this.verticalAlignSubject.next(value.verticalAlign);
    }
  }

  getElementAttribute(): ElementAttribute | undefined {
    return undefined;
  }

  updateElementAttribute<K extends ElementAttribute>(attribute: string, value: K | undefined): void {

  }
  /**
   * Whenever the drag starts we have to find the suitable container for it
   * so this function will tell the drop list register about the same
   * @param event
   */
  protected dragMoved(event: CdkDragMove) {
    this.dropListRegistry.dragMoved(event);
    // close the sub menu
  }
  /**
   * Called when the drag is released
   * @param event
   */
  protected dragReleased(event: CdkDragRelease) {
    this.dropListRegistry.dragReleased(event)
  }
  /**
   * An email block is dropped in this column, lets process it
   * @param $event
   */
  public elementDrop(event: CdkDragDrop<EmailBlock[], any>) {
    // multiple cases
    if (event.previousContainer.id === ELEMENT_TOOLBAR_ID || event.previousContainer.id.endsWith('-listing')) {
      // IF the element is dragged from teh toolbar or the listing that is opened from the toolbar, we have this code
      // but the listing drop-list should end with -listing, so we can identify it
      const blockConfig: EmailBlock = <EmailBlock>event.item.data;
      if (blockConfig) {
        // now we add this to the block
        this.canvasService.addBlockToDropList(this.id, event.currentIndex, blockConfig);
      }
    } else if (event.previousContainer.id === this.id) {
      // the structure is being reshuffled
      this.canvasService.shuffleBlockInDropList(event.previousIndex, event.currentIndex, this.id);
    } else {
      // we are moving a block from 1 DL to another DL
      const blockConfig: { dropListId: string, index: number, block: EmailBlock, type: string} = event.item.data;
      if (blockConfig) {
        // now we add this to the block
        this.canvasService.moveBlockFromDropListToAnotherDropList(blockConfig.dropListId, blockConfig.index, this.id, event.currentIndex);
      }
    }
  }
}
