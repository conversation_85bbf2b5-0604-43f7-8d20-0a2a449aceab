.unica-email-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; // Prevent scrollbars from fixed positioned drop zones

  // Drop zone styles positioned over the entire component
  .drop-zone-left,
  .drop-zone-right {
    position: absolute; // Fixed to viewport - no space in document flow
    top: 0;
    width: 60px;
    height: 100%;
    background: rgba(3, 141, 153, 0.1);
    border: 0.5px dashed rgba(3, 141, 153, 0.3);
    display: none; // Hidden by default - takes NO space
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: all 0.2s ease;
    pointer-events: none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    overflow: hidden;

    &.visible {
      display: flex;
      pointer-events: auto;
      opacity: 1;
      background: rgba(3, 141, 153, 0.2);
      border-color: var(--unica-primary, #038d99);

      &:hover,
      &.hover {
        background: rgba(3, 141, 153, 0.4);
        border-style: solid;
        border-width: 1px;
      }
    }

    .drop-zone-indicator {
      text-align: center;
      color: var(--unica-primary, #038d99);
      font-weight: 600;

      .drop-zone-icon {
        font-size: 24px;
        margin-bottom: 8px;
        color: white;
      }

      .drop-zone-text {
        font-size: 12px;
        writing-mode: vertical-rl;
        text-orientation: mixed;
        color: white;
        font-weight: bold;
      }
    }
  }

  .drop-zone-left {
    left: 0;
    border-right: 0.5px dashed rgba(3, 141, 153, 0.3);
    border-left: none;
  }

  .drop-zone-right {
    right: 0;
    border-left: 0.5px dashed rgba(3, 141, 153, 0.3);
    border-right: none;
  }

  mat-drawer-container {
    height: 100%;
    width: 100%;
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden; // Prevent any scrollbars from drop zones
  }

  .drawer {
    width: 70px !important;
    position: relative;
    min-height: 100vh;
    z-index: 100;
  }

  // Ensure drawer content is visible
  mat-drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
