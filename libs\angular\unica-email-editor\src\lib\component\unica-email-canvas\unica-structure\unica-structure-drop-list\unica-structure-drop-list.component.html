<div
  style="width: 100%; height: 100%"
  tabindex="0"
  (click)="focus($event)"
  (keydown)="focus($event)"
  [elementVerticalAlign]="elementVerticalAlign$ | async"
>
  <focus-overlay
    #focusOverLay
    *ngrxLet="column$; let colDef"
    [items]="columnToolbarItems"
    [nodeWidth]="colDef?.width ?? null"
    [draggable]="false"
    (select)="performAction($event)"
    [nodeBackground]="colDef?.background ?? null"
    [nodeBorder]="colDef?.border ?? null"
    [isActive]="(isFocused$ | async) ?? false"
  >
    <ng-container *ngrxLet="elements$; let elements">
      <div
        class="structure-column-drop-list element-drop-list"
        *ngrxLet="isElementBeingDropped$; let elementBeingDropped"
        [ngClass]="{
          'drop-active': elementBeingDropped,
          'structure-min-height': elements.length === 0,
        }"
        id="{{ id }}"
        #container="cdkDropList"
        cdkDropList
        [cdkDropListData]="elements"
        [cdkDropListEnterPredicate]="canElementBeDropped.bind(this)"
        [cdkDropListConnectedTo]="dropListRegistry.dropLists"
        (cdkDropListDropped)="elementDrop($event)"
        >
        <!--When the canvas is empty-->
        <empty-element
          *ngIf="elements.length === 0 && !elementBeingDropped"
          (select)="performAction($event)"
          [elements]="emptyStructureItems"
        >
        </empty-element>

        <!-- Iterate & create the Structures-->
        @for (item of elements; track $index) {
          <div
            cdkDrag
            class="canvas-draggable-element"
            style="width: 100%; height: 100%"
            (cdkDragMoved)="dragMoved($event)"
            (cdkDragReleased)="dragReleased($event)"
            [cdkDragData]="generateDragData(item, $index)"
          >
            <ng-container [ngSwitch]="item.type">
              <text-block
                *ngSwitchCase="'text'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></text-block>
              <image-block
                *ngSwitchCase="'image'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></image-block>
              <button-block
                *ngSwitchCase="'button'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></button-block>
              <divider-block
                *ngSwitchCase="'divider'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></divider-block>
              <spacer-block
                *ngSwitchCase="'spacer'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></spacer-block>
              <webpage-block
                *ngSwitchCase="'webpage'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></webpage-block>
              <preferences-unsubscribe-block
                *ngSwitchCase="'preferences-unsubscribe-link'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></preferences-unsubscribe-block>
              <content-connector-block
                *ngSwitchCase="'content-connector'"
                [block]="item"
                [dropListId]="id"
                [blockIndex]="$index"
              ></content-connector-block>
              <div *cdkDragPlaceholder>Place holder</div>
              <drag-preview *cdkDragPreview> TEST </drag-preview>
            </ng-container>
          </div>
        }
      </div>
    </ng-container>
  </focus-overlay>
</div>
