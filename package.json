{"name": "@unica-web/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "devDependencies": {"@angular-architects/module-federation": "^18.0.6", "@angular-architects/module-federation-tools": "^18.0.6", "@angular-devkit/build-angular": "^18.1.4", "@angular-devkit/core": "^18.1.4", "@angular-devkit/schematics": "^18.1.4", "@angular/cli": "^18.1.4", "@angular/compiler-cli": "^18.1.4", "@angular/language-service": "^18.1.4", "@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@module-federation/enhanced": "0.7.6", "@nx/angular": "^20.4.3", "@nx/cypress": "20.2.1", "@nx/devkit": "20.2.1", "@nx/esbuild": "20.2.1", "@nx/eslint": "20.2.1", "@nx/eslint-plugin": "20.2.1", "@nx/jest": "20.2.1", "@nx/js": "20.2.1", "@nx/module-federation": "20.3.0", "@nx/playwright": "20.2.1", "@nx/react": "^20.2.1", "@nx/storybook": "20.4.5", "@nx/web": "20.3.0", "@nx/webpack": "20.3.0", "@nx/workspace": "20.2.1", "@playwright/test": "^1.36.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@schematics/angular": "~19.0.0", "@storybook/addon-controls": "8.5.7", "@storybook/addon-essentials": "8.5.7", "@storybook/addon-interactions": "8.5.7", "@storybook/addon-styling-webpack": "1.0.1", "@storybook/angular": "8.5.7", "@storybook/core-server": "8.5.7", "@storybook/test": "8.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@typescript-eslint/utils": "^8.19.0", "angular-eslint": "^19.0.0", "autoprefixer": "^10.4.0", "babel-jest": "^29.7.0", "cypress": "^13.13.0", "esbuild": "^0.19.2", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-preset-angular": "~14.4.0", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "~18.2.1", "nx": "^20.2.1", "postcss": "^8.4.5", "postcss-url": "~10.1.3", "prettier": "^3.4.2", "react-refresh": "^0.10.0", "storybook": "8.5.7", "tailwindcss": "^3.0.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "^5.5.2", "typescript-eslint": "^8.13.0", "verdaccio": "^5.0.4"}, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/material": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@ngneat/until-destroy": "^9.2.3", "@ngrx/component": "^18.0.0", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^16.0.0", "dayjs": "^1.11.13", "gridstack": "^10.3.1", "lodash-es": "4.17.21", "material-design-icons": "^3.0.1", "ngx-color-picker": "^17.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.11.2", "rxjs": "~7.8.0", "zone.js": "~0.14.10", "ngx-quill": "^26.0.1", "quill": "^2.0.2"}, "nx": {"includedScripts": []}}