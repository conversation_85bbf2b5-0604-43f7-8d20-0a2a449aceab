import { UnicaSideNavElementItemConfig } from '@hcl/unica-common';
import { EmailBlock, UnicaStructure } from './email';


/**
 * This is the type for the background
 */
export enum EmailElementType {
  FONT,
  STRUCTURE,
  IMAGE,
  TEXT,
  BUTTON,
  SPACER,
  DIVIDER,
  SOC<PERSON><PERSON>,
  WEBPAGE,
  HTML,
  CONT<PERSON>TCONNECTOR,
  CANVAS_SETTINGS,
  IMAGE_SETTINGS,
  BUTTON_SETTINGS,
  DIVIDER_SETTINGS,
  SPACER_SETTINGS,
  WEBPAGE_SETTINGS,
  PREFERENCES_UNSUBSCRIBE_SETTINGS,
  STRUCTURE_SETTINGS,
  COLUMN_SETTINGS,
  TEXT_SETTINGS,
  CONTENT_CONNECTOR_SETTINGS
}
// export type EmailElementType =
//   'font' | 'structure' | 'image' | 'setting' | 'spacer' | 'image-settings' | 'column-settings' | 'canvas-settings';

/**
 * The extended version of UnicaSideNavElementItemConfig
 */
export interface EmailElementToolbarConfig extends UnicaSideNavElementItemConfig {
  draggable?: boolean;
  type: EmailElementType;
  config?: EmailBlock | UnicaStructure
}
